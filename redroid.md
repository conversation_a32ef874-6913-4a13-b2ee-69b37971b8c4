引言
Redroid是什么，不想多说，一个词总结: 云手机

准备工作
AOSP代码一堆，编译起来要命，所以租个高配服务器，笔者用的是Vultr的云服务器，6c 16G 320G配置，五个小时才编译出来

装docker，装repo，装git-lfs：

bash
sudo apt install docker.io repo git-lfs
创用户，自己拿adduser创个，教程一堆，创完给root权限，然后切到这个用户下。

同步代码:

bash
# 这里使用了ice-black-tea大佬的仓库，在此对他表示感谢。
mkdir ~/redroid && cd ~/redroid
sudo repo init -u https://github.com/redroid-rockchip/platform_manifests.git -b redroid-12.0.0 --depth=1 --git-lfs
sudo repo sync -c
同步webview：

bash
sudo apt install git-lfs
sudo repo forall -g lfs -c git lfs pull
修改build/soong/cc/config/global.go，向commonGlobalCflags数组添加全局cflags "-DANDROID_12"

代码同步完成后将源码的所有者改为当前用户:

bash
sudo chown -R `whoami`:`whoami` ~/redroid
编译
构建编译镜像：

bash
cd ~/ && git clone https://github.com/remote-android/redroid-doc.git
cd redroid-doc/android-builder-docker/
docker build --build-arg userid=$(id -u) --build-arg groupid=$(id -g) --build-arg username=$(id -un) -t redroid-builder .
启动镜像：

bash
# 建议丢到screen里面
docker run -it --rm --hostname redroid-builder --name redroid-builder -v ~/redroid:/src redroid-builder
在镜像命令行中输入：

bash
cd /src
. build/envsetup.sh
lunch redroid_arm64-userdebug
export TARGET_BOARD_PLATFORM_GPU=mali-G52 TARGET_RK_GRALLOC_VERSION=4
开始编译：

bash
m
打包
回到主机命令行：

bash
cd ~/redroid/out/target/product/redroid_arm64
sudo mount system.img system -o ro
sudo mount vendor.img vendor -o ro
sudo tar --xattrs -I "xz -zT0" -vcf redroid.tar.xz vendor -C system --exclude="./vendor" . 
sudo umount system vendor
导入
打完包把镜像推开发板上，然后导入：

bash
xz -dcT0 redroid.tar.xz | docker import -c 'ENTRYPOINT ["/init", "androidboot.hardware=redroid"]' - redroid
运行
bash
docker run -d -p 5555:5555 -v ~/redroid-data:/data --name redroid --privileged redroid androidboot.redroid_height=1920 androidboot.redroid_width=1080
