为RK3588适配LineageOS版Redroid镜像
提示

redroid-rk3588项目已添加LineageOS 20的支持：
GitHub链接

起因
长篇大论懒得说，一句话总结：byd谷歌把AOSP搞成了毛坯房，根本没法住人，换LineageOS了。

克隆代码
清华源有教程，自己去看，顺便要把redroid官方的那些组件也加到里面去，也就是克隆local_manifests那一步。这次弄的是基于Android 13的LineageOS 20，如果需要gapps的话，从MindTheGapps那边弄到项目里面就可以了。对了， Android 13的gapps，代号为tau。
然后，要把rk3588相关的一些组件，都弄到源码树里面来。去khadas那边偷，github上他们传的有很多库，参照的android-manifest库，khadas-edge2-android13分支，按照路径把对应的库拉到对应的位置就可以了:

hardware/rockchip/libbaseparameter
hardware/rockchip/libgralloc/bifrost
hardware/rockchip/libhardware_rockchip
hardware/rockchip/librga
hardware/rockchip/librkvpu
device/rockchip/common
vendor/rockchip/common
external/libdrm
注意: device/rockchip/common要从device/khadas/common拉下来，拉完全局搜索替换khadas为rockchip，然后删除charger的Android.bp文件。

修改代码
patches从redroid那边弄过来，然后做个软链接，把android-13.0.0-r83链接到lineage-20上，这样apply-patch.sh才能正确识别到LineageOS的tag，然后正常打patch就行。
在device/redroid/redroid.mk里面，引入LineageOS相关的东西：

makefile
LINAGE_BUILD := redroid
$(call inherit-product, vendor/lineage/config/common_full_phone.mk)
vendor/redroid/vendor.mk，把这些东西加进去:

makefile
TARGET_BOARD_PLATFORM := rk3588
TARGET_BOARD_PLATFORM_GPU := mali-G610
TARGET_BOARD_HARDWARE := rk30board
TARGET_RK_GRALLOC_VERSION := 4
BOARD_USE_DRM := true

PRODUCT_HAVE_RKVPU := true

BUILD_BROKEN_ELF_PREBUILT_PRODUCT_COPY_FILES := true
BUILD_BROKEN_DUP_RULES := true

include device/rockchip/common/gralloc.device.mk

PRODUCT_BROKEN_VERIFY_USES_LIBRARIES := true


BOARD_VENDOR_GPU_PLATFORM := bifrost

$(call inherit-product, $(SRC_TARGET_DIR)/product/core_64_bit.mk)

PRODUCT_PROPERTY_OVERRIDES += \
    ro.vendor.mpp_buf_type=1

PRODUCT_PACKAGES += \
    android.hardware.graphics.allocator-V1-service \
    android.hardware.graphics.allocator-V1-$(BOARD_VENDOR_GPU_PLATFORM) \
    android.hardware.graphics.allocator-V1-arm \
    android.hardware.graphics.mapper@4.0-impl-$(BOARD_VENDOR_GPU_PLATFORM)

DEVICE_MANIFEST_FILE += \
    device/rockchip/common/manifests/<EMAIL>

$(call inherit-product, device/rockchip/common/rootdir/rootdir.mk)
$(call inherit-product, device/rockchip/common/modules/mediacodec.mk)
$(call inherit-product, vendor/rockchip/common/device-vendor.mk)
加好之后gpu_config.sh脚本修改：

bash
#!/system/bin/sh
setprop ro.hardware.egl mali
setprop ro.hardware.gralloc redroid
setprop ro.boot.redroid_fps 60

redroid_fps=$(getprop ro.boot.redroid_fps)
if [ -z "$redroid_fps" ] || ! [[ $redroid_fps =~ ^[0-9]+$ ]]
    then
            setprop ro.boot.redroid_fps 60
else
    if [ $redroid_fps -gt 1 ] && [ $redroid_fps -lt 120 ]
    then
            setprop ro.boot.redroid_fps $redroid_fps
    else
            setprop ro.boot.redroid_fps 60
    fi
fi
redroid.common.rc，on-early-init里面加入：

rc
    chmod 0777 /dev/rga
    chmod 0666 /dev/uhid
    chmod 0777 /dev/dri
    chmod 0777 /dev/dri/card0
    chmod 0777 /dev/dri/card1
    chmod 0777 /dev/dri/renderD128
    chmod 0777 /dev/dri/renderD129
    chmod 0777 /dev/mpp_service
    chmod 0666 /dev/mali0

    chmod 0444 /dev/dma_heap/cma
    chown system system /dev/dma_heap/cma
    chmod 0444 /dev/dma_heap/cma-uncached
    chown system system /dev/dma_heap/cma-uncached
    chmod 0444 /dev/dma_heap/system
    chown system system /dev/dma_heap/system
    chmod 0444 /dev/dma_heap/system-dma32
    chown system system /dev/dma_heap/system-dma32
    chmod 0444 /dev/dma_heap/system-uncached
    chown system system /dev/dma_heap/system-uncached
    chmod 0444 /dev/dma_heap/system-uncached-dma32
    chown system system /dev/dma_heap/system-uncached-dma32
    
    # disable codec2 by default, DMA Heap required
    setprop debug.stagefright.ccodec 4
搞定之后禁掉redroid的omx…… 好累，不想写了 可能还有什么遗漏，看日志排错吧，我这一时半会也想不起来了。
之后就可以添加别的东西了，比如说安全显示器啊，虚假WiFi啊之类的，参考我博客文章就可以了。

编译
懒得说，参考博客文章吧，累了。
lunch redroid_arm64-user就构建出user镜像了，redroid_arm64-userdebug就是userdebug镜像，按需来。 如果你要构建user镜像的话，adb不会自启，而且会默认开启验证。 要打开adb自启的话，需要设置USB默认模式为adb:

makefile
PRODUCT_PRODUCT_PROPERTIES += \
    persist.sys.usb.config=adb \
然后，adb代码里面做个判断，如果/data/misc/adb/adb_keys文件不存在，就不打开验证。代码很简单，这里就不写了。